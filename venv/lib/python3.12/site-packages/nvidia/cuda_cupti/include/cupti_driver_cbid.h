
// *************************************************************************
//      Definitions of indices for API functions, unique across entire API
// *************************************************************************

// This file is generated.  Any changes you make will be lost during the next clean build.
// CUDA public interface, for type definitions and cu* function prototypes

typedef enum CUpti_driver_api_trace_cbid_enum {
    CUPTI_DRIVER_TRACE_CBID_INVALID                                                        = 0,
    CUPTI_DRIVER_TRACE_CBID_cuInit                                                         = 1,
    CUPTI_DRIVER_TRACE_CBID_cuDriverGetVersion                                             = 2,
    CUPTI_DRIVER_TRACE_CBID_cuDeviceGet                                                    = 3,
    CUP<PERSON>_DRIVER_TRACE_CBID_cuDeviceGetCount                                               = 4,
    CUP<PERSON>_DRIVER_TRACE_CBID_cuDeviceGetName                                                = 5,
    CUPTI_DRIVER_TRACE_CBID_cuDeviceComputeCapability                                      = 6,
    CUPTI_DRIVER_TRACE_CBID_cuDeviceTotalMem                                               = 7,
    CUPTI_DRIVER_TRACE_CBID_cuDeviceGetProperties                                          = 8,
    CUPTI_DRIVER_TRACE_CBID_cuDeviceGetAttribute                                           = 9,
    CUPTI_DRIVER_TRACE_CBID_cuCtxCreate                                                    = 10,
    CUPTI_DRIVER_TRACE_CBID_cuCtxDestroy                                                   = 11,
    CUPTI_DRIVER_TRACE_CBID_cuCtxAttach                                                    = 12,
    CUPTI_DRIVER_TRACE_CBID_cuCtxDetach                                                    = 13,
    CUPTI_DRIVER_TRACE_CBID_cuCtxPushCurrent                                               = 14,
    CUPTI_DRIVER_TRACE_CBID_cuCtxPopCurrent                                                = 15,
    CUPTI_DRIVER_TRACE_CBID_cuCtxGetDevice                                                 = 16,
    CUPTI_DRIVER_TRACE_CBID_cuCtxSynchronize                                               = 17,
    CUPTI_DRIVER_TRACE_CBID_cuModuleLoad                                                   = 18,
    CUPTI_DRIVER_TRACE_CBID_cuModuleLoadData                                               = 19,
    CUPTI_DRIVER_TRACE_CBID_cuModuleLoadDataEx                                             = 20,
    CUPTI_DRIVER_TRACE_CBID_cuModuleLoadFatBinary                                          = 21,
    CUPTI_DRIVER_TRACE_CBID_cuModuleUnload                                                 = 22,
    CUPTI_DRIVER_TRACE_CBID_cuModuleGetFunction                                            = 23,
    CUPTI_DRIVER_TRACE_CBID_cuModuleGetGlobal                                              = 24,
    CUPTI_DRIVER_TRACE_CBID_cu64ModuleGetGlobal                                            = 25,
    CUPTI_DRIVER_TRACE_CBID_cuModuleGetTexRef                                              = 26,
    CUPTI_DRIVER_TRACE_CBID_cuMemGetInfo                                                   = 27,
    CUPTI_DRIVER_TRACE_CBID_cu64MemGetInfo                                                 = 28,
    CUPTI_DRIVER_TRACE_CBID_cuMemAlloc                                                     = 29,
    CUPTI_DRIVER_TRACE_CBID_cu64MemAlloc                                                   = 30,
    CUPTI_DRIVER_TRACE_CBID_cuMemAllocPitch                                                = 31,
    CUPTI_DRIVER_TRACE_CBID_cu64MemAllocPitch                                              = 32,
    CUPTI_DRIVER_TRACE_CBID_cuMemFree                                                      = 33,
    CUPTI_DRIVER_TRACE_CBID_cu64MemFree                                                    = 34,
    CUPTI_DRIVER_TRACE_CBID_cuMemGetAddressRange                                           = 35,
    CUPTI_DRIVER_TRACE_CBID_cu64MemGetAddressRange                                         = 36,
    CUPTI_DRIVER_TRACE_CBID_cuMemAllocHost                                                 = 37,
    CUPTI_DRIVER_TRACE_CBID_cuMemFreeHost                                                  = 38,
    CUPTI_DRIVER_TRACE_CBID_cuMemHostAlloc                                                 = 39,
    CUPTI_DRIVER_TRACE_CBID_cuMemHostGetDevicePointer                                      = 40,
    CUPTI_DRIVER_TRACE_CBID_cu64MemHostGetDevicePointer                                    = 41,
    CUPTI_DRIVER_TRACE_CBID_cuMemHostGetFlags                                              = 42,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyHtoD                                                   = 43,
    CUPTI_DRIVER_TRACE_CBID_cu64MemcpyHtoD                                                 = 44,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyDtoH                                                   = 45,
    CUPTI_DRIVER_TRACE_CBID_cu64MemcpyDtoH                                                 = 46,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyDtoD                                                   = 47,
    CUPTI_DRIVER_TRACE_CBID_cu64MemcpyDtoD                                                 = 48,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyDtoA                                                   = 49,
    CUPTI_DRIVER_TRACE_CBID_cu64MemcpyDtoA                                                 = 50,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyAtoD                                                   = 51,
    CUPTI_DRIVER_TRACE_CBID_cu64MemcpyAtoD                                                 = 52,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyHtoA                                                   = 53,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyAtoH                                                   = 54,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyAtoA                                                   = 55,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpy2D                                                     = 56,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpy2DUnaligned                                            = 57,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpy3D                                                     = 58,
    CUPTI_DRIVER_TRACE_CBID_cu64Memcpy3D                                                   = 59,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyHtoDAsync                                              = 60,
    CUPTI_DRIVER_TRACE_CBID_cu64MemcpyHtoDAsync                                            = 61,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyDtoHAsync                                              = 62,
    CUPTI_DRIVER_TRACE_CBID_cu64MemcpyDtoHAsync                                            = 63,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyDtoDAsync                                              = 64,
    CUPTI_DRIVER_TRACE_CBID_cu64MemcpyDtoDAsync                                            = 65,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyHtoAAsync                                              = 66,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyAtoHAsync                                              = 67,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpy2DAsync                                                = 68,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpy3DAsync                                                = 69,
    CUPTI_DRIVER_TRACE_CBID_cu64Memcpy3DAsync                                              = 70,
    CUPTI_DRIVER_TRACE_CBID_cuMemsetD8                                                     = 71,
    CUPTI_DRIVER_TRACE_CBID_cu64MemsetD8                                                   = 72,
    CUPTI_DRIVER_TRACE_CBID_cuMemsetD16                                                    = 73,
    CUPTI_DRIVER_TRACE_CBID_cu64MemsetD16                                                  = 74,
    CUPTI_DRIVER_TRACE_CBID_cuMemsetD32                                                    = 75,
    CUPTI_DRIVER_TRACE_CBID_cu64MemsetD32                                                  = 76,
    CUPTI_DRIVER_TRACE_CBID_cuMemsetD2D8                                                   = 77,
    CUPTI_DRIVER_TRACE_CBID_cu64MemsetD2D8                                                 = 78,
    CUPTI_DRIVER_TRACE_CBID_cuMemsetD2D16                                                  = 79,
    CUPTI_DRIVER_TRACE_CBID_cu64MemsetD2D16                                                = 80,
    CUPTI_DRIVER_TRACE_CBID_cuMemsetD2D32                                                  = 81,
    CUPTI_DRIVER_TRACE_CBID_cu64MemsetD2D32                                                = 82,
    CUPTI_DRIVER_TRACE_CBID_cuFuncSetBlockShape                                            = 83,
    CUPTI_DRIVER_TRACE_CBID_cuFuncSetSharedSize                                            = 84,
    CUPTI_DRIVER_TRACE_CBID_cuFuncGetAttribute                                             = 85,
    CUPTI_DRIVER_TRACE_CBID_cuFuncSetCacheConfig                                           = 86,
    CUPTI_DRIVER_TRACE_CBID_cuArrayCreate                                                  = 87,
    CUPTI_DRIVER_TRACE_CBID_cuArrayGetDescriptor                                           = 88,
    CUPTI_DRIVER_TRACE_CBID_cuArrayDestroy                                                 = 89,
    CUPTI_DRIVER_TRACE_CBID_cuArray3DCreate                                                = 90,
    CUPTI_DRIVER_TRACE_CBID_cuArray3DGetDescriptor                                         = 91,
    CUPTI_DRIVER_TRACE_CBID_cuTexRefCreate                                                 = 92,
    CUPTI_DRIVER_TRACE_CBID_cuTexRefDestroy                                                = 93,
    CUPTI_DRIVER_TRACE_CBID_cuTexRefSetArray                                               = 94,
    CUPTI_DRIVER_TRACE_CBID_cuTexRefSetAddress                                             = 95,
    CUPTI_DRIVER_TRACE_CBID_cu64TexRefSetAddress                                           = 96,
    CUPTI_DRIVER_TRACE_CBID_cuTexRefSetAddress2D                                           = 97,
    CUPTI_DRIVER_TRACE_CBID_cu64TexRefSetAddress2D                                         = 98,
    CUPTI_DRIVER_TRACE_CBID_cuTexRefSetFormat                                              = 99,
    CUPTI_DRIVER_TRACE_CBID_cuTexRefSetAddressMode                                         = 100,
    CUPTI_DRIVER_TRACE_CBID_cuTexRefSetFilterMode                                          = 101,
    CUPTI_DRIVER_TRACE_CBID_cuTexRefSetFlags                                               = 102,
    CUPTI_DRIVER_TRACE_CBID_cuTexRefGetAddress                                             = 103,
    CUPTI_DRIVER_TRACE_CBID_cu64TexRefGetAddress                                           = 104,
    CUPTI_DRIVER_TRACE_CBID_cuTexRefGetArray                                               = 105,
    CUPTI_DRIVER_TRACE_CBID_cuTexRefGetAddressMode                                         = 106,
    CUPTI_DRIVER_TRACE_CBID_cuTexRefGetFilterMode                                          = 107,
    CUPTI_DRIVER_TRACE_CBID_cuTexRefGetFormat                                              = 108,
    CUPTI_DRIVER_TRACE_CBID_cuTexRefGetFlags                                               = 109,
    CUPTI_DRIVER_TRACE_CBID_cuParamSetSize                                                 = 110,
    CUPTI_DRIVER_TRACE_CBID_cuParamSeti                                                    = 111,
    CUPTI_DRIVER_TRACE_CBID_cuParamSetf                                                    = 112,
    CUPTI_DRIVER_TRACE_CBID_cuParamSetv                                                    = 113,
    CUPTI_DRIVER_TRACE_CBID_cuParamSetTexRef                                               = 114,
    CUPTI_DRIVER_TRACE_CBID_cuLaunch                                                       = 115,
    CUPTI_DRIVER_TRACE_CBID_cuLaunchGrid                                                   = 116,
    CUPTI_DRIVER_TRACE_CBID_cuLaunchGridAsync                                              = 117,
    CUPTI_DRIVER_TRACE_CBID_cuEventCreate                                                  = 118,
    CUPTI_DRIVER_TRACE_CBID_cuEventRecord                                                  = 119,
    CUPTI_DRIVER_TRACE_CBID_cuEventQuery                                                   = 120,
    CUPTI_DRIVER_TRACE_CBID_cuEventSynchronize                                             = 121,
    CUPTI_DRIVER_TRACE_CBID_cuEventDestroy                                                 = 122,
    CUPTI_DRIVER_TRACE_CBID_cuEventElapsedTime                                             = 123,
    CUPTI_DRIVER_TRACE_CBID_cuStreamCreate                                                 = 124,
    CUPTI_DRIVER_TRACE_CBID_cuStreamQuery                                                  = 125,
    CUPTI_DRIVER_TRACE_CBID_cuStreamSynchronize                                            = 126,
    CUPTI_DRIVER_TRACE_CBID_cuStreamDestroy                                                = 127,
    CUPTI_DRIVER_TRACE_CBID_cuGraphicsUnregisterResource                                   = 128,
    CUPTI_DRIVER_TRACE_CBID_cuGraphicsSubResourceGetMappedArray                            = 129,
    CUPTI_DRIVER_TRACE_CBID_cuGraphicsResourceGetMappedPointer                             = 130,
    CUPTI_DRIVER_TRACE_CBID_cu64GraphicsResourceGetMappedPointer                           = 131,
    CUPTI_DRIVER_TRACE_CBID_cuGraphicsResourceSetMapFlags                                  = 132,
    CUPTI_DRIVER_TRACE_CBID_cuGraphicsMapResources                                         = 133,
    CUPTI_DRIVER_TRACE_CBID_cuGraphicsUnmapResources                                       = 134,
    CUPTI_DRIVER_TRACE_CBID_cuGetExportTable                                               = 135,
    CUPTI_DRIVER_TRACE_CBID_cuCtxSetLimit                                                  = 136,
    CUPTI_DRIVER_TRACE_CBID_cuCtxGetLimit                                                  = 137,
    CUPTI_DRIVER_TRACE_CBID_cuD3D10GetDevice                                               = 138,
    CUPTI_DRIVER_TRACE_CBID_cuD3D10CtxCreate                                               = 139,
    CUPTI_DRIVER_TRACE_CBID_cuGraphicsD3D10RegisterResource                                = 140,
    CUPTI_DRIVER_TRACE_CBID_cuD3D10RegisterResource                                        = 141,
    CUPTI_DRIVER_TRACE_CBID_cuD3D10UnregisterResource                                      = 142,
    CUPTI_DRIVER_TRACE_CBID_cuD3D10MapResources                                            = 143,
    CUPTI_DRIVER_TRACE_CBID_cuD3D10UnmapResources                                          = 144,
    CUPTI_DRIVER_TRACE_CBID_cuD3D10ResourceSetMapFlags                                     = 145,
    CUPTI_DRIVER_TRACE_CBID_cuD3D10ResourceGetMappedArray                                  = 146,
    CUPTI_DRIVER_TRACE_CBID_cuD3D10ResourceGetMappedPointer                                = 147,
    CUPTI_DRIVER_TRACE_CBID_cuD3D10ResourceGetMappedSize                                   = 148,
    CUPTI_DRIVER_TRACE_CBID_cuD3D10ResourceGetMappedPitch                                  = 149,
    CUPTI_DRIVER_TRACE_CBID_cuD3D10ResourceGetSurfaceDimensions                            = 150,
    CUPTI_DRIVER_TRACE_CBID_cuD3D11GetDevice                                               = 151,
    CUPTI_DRIVER_TRACE_CBID_cuD3D11CtxCreate                                               = 152,
    CUPTI_DRIVER_TRACE_CBID_cuGraphicsD3D11RegisterResource                                = 153,
    CUPTI_DRIVER_TRACE_CBID_cuD3D9GetDevice                                                = 154,
    CUPTI_DRIVER_TRACE_CBID_cuD3D9CtxCreate                                                = 155,
    CUPTI_DRIVER_TRACE_CBID_cuGraphicsD3D9RegisterResource                                 = 156,
    CUPTI_DRIVER_TRACE_CBID_cuD3D9GetDirect3DDevice                                        = 157,
    CUPTI_DRIVER_TRACE_CBID_cuD3D9RegisterResource                                         = 158,
    CUPTI_DRIVER_TRACE_CBID_cuD3D9UnregisterResource                                       = 159,
    CUPTI_DRIVER_TRACE_CBID_cuD3D9MapResources                                             = 160,
    CUPTI_DRIVER_TRACE_CBID_cuD3D9UnmapResources                                           = 161,
    CUPTI_DRIVER_TRACE_CBID_cuD3D9ResourceSetMapFlags                                      = 162,
    CUPTI_DRIVER_TRACE_CBID_cuD3D9ResourceGetSurfaceDimensions                             = 163,
    CUPTI_DRIVER_TRACE_CBID_cuD3D9ResourceGetMappedArray                                   = 164,
    CUPTI_DRIVER_TRACE_CBID_cuD3D9ResourceGetMappedPointer                                 = 165,
    CUPTI_DRIVER_TRACE_CBID_cuD3D9ResourceGetMappedSize                                    = 166,
    CUPTI_DRIVER_TRACE_CBID_cuD3D9ResourceGetMappedPitch                                   = 167,
    CUPTI_DRIVER_TRACE_CBID_cuD3D9Begin                                                    = 168,
    CUPTI_DRIVER_TRACE_CBID_cuD3D9End                                                      = 169,
    CUPTI_DRIVER_TRACE_CBID_cuD3D9RegisterVertexBuffer                                     = 170,
    CUPTI_DRIVER_TRACE_CBID_cuD3D9MapVertexBuffer                                          = 171,
    CUPTI_DRIVER_TRACE_CBID_cuD3D9UnmapVertexBuffer                                        = 172,
    CUPTI_DRIVER_TRACE_CBID_cuD3D9UnregisterVertexBuffer                                   = 173,
    CUPTI_DRIVER_TRACE_CBID_cuGLCtxCreate                                                  = 174,
    CUPTI_DRIVER_TRACE_CBID_cuGraphicsGLRegisterBuffer                                     = 175,
    CUPTI_DRIVER_TRACE_CBID_cuGraphicsGLRegisterImage                                      = 176,
    CUPTI_DRIVER_TRACE_CBID_cuWGLGetDevice                                                 = 177,
    CUPTI_DRIVER_TRACE_CBID_cuGLInit                                                       = 178,
    CUPTI_DRIVER_TRACE_CBID_cuGLRegisterBufferObject                                       = 179,
    CUPTI_DRIVER_TRACE_CBID_cuGLMapBufferObject                                            = 180,
    CUPTI_DRIVER_TRACE_CBID_cuGLUnmapBufferObject                                          = 181,
    CUPTI_DRIVER_TRACE_CBID_cuGLUnregisterBufferObject                                     = 182,
    CUPTI_DRIVER_TRACE_CBID_cuGLSetBufferObjectMapFlags                                    = 183,
    CUPTI_DRIVER_TRACE_CBID_cuGLMapBufferObjectAsync                                       = 184,
    CUPTI_DRIVER_TRACE_CBID_cuGLUnmapBufferObjectAsync                                     = 185,
    CUPTI_DRIVER_TRACE_CBID_cuVDPAUGetDevice                                               = 186,
    CUPTI_DRIVER_TRACE_CBID_cuVDPAUCtxCreate                                               = 187,
    CUPTI_DRIVER_TRACE_CBID_cuGraphicsVDPAURegisterVideoSurface                            = 188,
    CUPTI_DRIVER_TRACE_CBID_cuGraphicsVDPAURegisterOutputSurface                           = 189,
    CUPTI_DRIVER_TRACE_CBID_cuModuleGetSurfRef                                             = 190,
    CUPTI_DRIVER_TRACE_CBID_cuSurfRefCreate                                                = 191,
    CUPTI_DRIVER_TRACE_CBID_cuSurfRefDestroy                                               = 192,
    CUPTI_DRIVER_TRACE_CBID_cuSurfRefSetFormat                                             = 193,
    CUPTI_DRIVER_TRACE_CBID_cuSurfRefSetArray                                              = 194,
    CUPTI_DRIVER_TRACE_CBID_cuSurfRefGetFormat                                             = 195,
    CUPTI_DRIVER_TRACE_CBID_cuSurfRefGetArray                                              = 196,
    CUPTI_DRIVER_TRACE_CBID_cu64DeviceTotalMem                                             = 197,
    CUPTI_DRIVER_TRACE_CBID_cu64D3D10ResourceGetMappedPointer                              = 198,
    CUPTI_DRIVER_TRACE_CBID_cu64D3D10ResourceGetMappedSize                                 = 199,
    CUPTI_DRIVER_TRACE_CBID_cu64D3D10ResourceGetMappedPitch                                = 200,
    CUPTI_DRIVER_TRACE_CBID_cu64D3D10ResourceGetSurfaceDimensions                          = 201,
    CUPTI_DRIVER_TRACE_CBID_cu64D3D9ResourceGetSurfaceDimensions                           = 202,
    CUPTI_DRIVER_TRACE_CBID_cu64D3D9ResourceGetMappedPointer                               = 203,
    CUPTI_DRIVER_TRACE_CBID_cu64D3D9ResourceGetMappedSize                                  = 204,
    CUPTI_DRIVER_TRACE_CBID_cu64D3D9ResourceGetMappedPitch                                 = 205,
    CUPTI_DRIVER_TRACE_CBID_cu64D3D9MapVertexBuffer                                        = 206,
    CUPTI_DRIVER_TRACE_CBID_cu64GLMapBufferObject                                          = 207,
    CUPTI_DRIVER_TRACE_CBID_cu64GLMapBufferObjectAsync                                     = 208,
    CUPTI_DRIVER_TRACE_CBID_cuD3D11GetDevices                                              = 209,
    CUPTI_DRIVER_TRACE_CBID_cuD3D11CtxCreateOnDevice                                       = 210,
    CUPTI_DRIVER_TRACE_CBID_cuD3D10GetDevices                                              = 211,
    CUPTI_DRIVER_TRACE_CBID_cuD3D10CtxCreateOnDevice                                       = 212,
    CUPTI_DRIVER_TRACE_CBID_cuD3D9GetDevices                                               = 213,
    CUPTI_DRIVER_TRACE_CBID_cuD3D9CtxCreateOnDevice                                        = 214,
    CUPTI_DRIVER_TRACE_CBID_cu64MemHostAlloc                                               = 215,
    CUPTI_DRIVER_TRACE_CBID_cuMemsetD8Async                                                = 216,
    CUPTI_DRIVER_TRACE_CBID_cu64MemsetD8Async                                              = 217,
    CUPTI_DRIVER_TRACE_CBID_cuMemsetD16Async                                               = 218,
    CUPTI_DRIVER_TRACE_CBID_cu64MemsetD16Async                                             = 219,
    CUPTI_DRIVER_TRACE_CBID_cuMemsetD32Async                                               = 220,
    CUPTI_DRIVER_TRACE_CBID_cu64MemsetD32Async                                             = 221,
    CUPTI_DRIVER_TRACE_CBID_cuMemsetD2D8Async                                              = 222,
    CUPTI_DRIVER_TRACE_CBID_cu64MemsetD2D8Async                                            = 223,
    CUPTI_DRIVER_TRACE_CBID_cuMemsetD2D16Async                                             = 224,
    CUPTI_DRIVER_TRACE_CBID_cu64MemsetD2D16Async                                           = 225,
    CUPTI_DRIVER_TRACE_CBID_cuMemsetD2D32Async                                             = 226,
    CUPTI_DRIVER_TRACE_CBID_cu64MemsetD2D32Async                                           = 227,
    CUPTI_DRIVER_TRACE_CBID_cu64ArrayCreate                                                = 228,
    CUPTI_DRIVER_TRACE_CBID_cu64ArrayGetDescriptor                                         = 229,
    CUPTI_DRIVER_TRACE_CBID_cu64Array3DCreate                                              = 230,
    CUPTI_DRIVER_TRACE_CBID_cu64Array3DGetDescriptor                                       = 231,
    CUPTI_DRIVER_TRACE_CBID_cu64Memcpy2D                                                   = 232,
    CUPTI_DRIVER_TRACE_CBID_cu64Memcpy2DUnaligned                                          = 233,
    CUPTI_DRIVER_TRACE_CBID_cu64Memcpy2DAsync                                              = 234,
    CUPTI_DRIVER_TRACE_CBID_cuCtxCreate_v2                                                 = 235,
    CUPTI_DRIVER_TRACE_CBID_cuD3D10CtxCreate_v2                                            = 236,
    CUPTI_DRIVER_TRACE_CBID_cuD3D11CtxCreate_v2                                            = 237,
    CUPTI_DRIVER_TRACE_CBID_cuD3D9CtxCreate_v2                                             = 238,
    CUPTI_DRIVER_TRACE_CBID_cuGLCtxCreate_v2                                               = 239,
    CUPTI_DRIVER_TRACE_CBID_cuVDPAUCtxCreate_v2                                            = 240,
    CUPTI_DRIVER_TRACE_CBID_cuModuleGetGlobal_v2                                           = 241,
    CUPTI_DRIVER_TRACE_CBID_cuMemGetInfo_v2                                                = 242,
    CUPTI_DRIVER_TRACE_CBID_cuMemAlloc_v2                                                  = 243,
    CUPTI_DRIVER_TRACE_CBID_cuMemAllocPitch_v2                                             = 244,
    CUPTI_DRIVER_TRACE_CBID_cuMemFree_v2                                                   = 245,
    CUPTI_DRIVER_TRACE_CBID_cuMemGetAddressRange_v2                                        = 246,
    CUPTI_DRIVER_TRACE_CBID_cuMemHostGetDevicePointer_v2                                   = 247,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpy_v2                                                    = 248,
    CUPTI_DRIVER_TRACE_CBID_cuMemsetD8_v2                                                  = 249,
    CUPTI_DRIVER_TRACE_CBID_cuMemsetD16_v2                                                 = 250,
    CUPTI_DRIVER_TRACE_CBID_cuMemsetD32_v2                                                 = 251,
    CUPTI_DRIVER_TRACE_CBID_cuMemsetD2D8_v2                                                = 252,
    CUPTI_DRIVER_TRACE_CBID_cuMemsetD2D16_v2                                               = 253,
    CUPTI_DRIVER_TRACE_CBID_cuMemsetD2D32_v2                                               = 254,
    CUPTI_DRIVER_TRACE_CBID_cuTexRefSetAddress_v2                                          = 255,
    CUPTI_DRIVER_TRACE_CBID_cuTexRefSetAddress2D_v2                                        = 256,
    CUPTI_DRIVER_TRACE_CBID_cuTexRefGetAddress_v2                                          = 257,
    CUPTI_DRIVER_TRACE_CBID_cuGraphicsResourceGetMappedPointer_v2                          = 258,
    CUPTI_DRIVER_TRACE_CBID_cuDeviceTotalMem_v2                                            = 259,
    CUPTI_DRIVER_TRACE_CBID_cuD3D10ResourceGetMappedPointer_v2                             = 260,
    CUPTI_DRIVER_TRACE_CBID_cuD3D10ResourceGetMappedSize_v2                                = 261,
    CUPTI_DRIVER_TRACE_CBID_cuD3D10ResourceGetMappedPitch_v2                               = 262,
    CUPTI_DRIVER_TRACE_CBID_cuD3D10ResourceGetSurfaceDimensions_v2                         = 263,
    CUPTI_DRIVER_TRACE_CBID_cuD3D9ResourceGetSurfaceDimensions_v2                          = 264,
    CUPTI_DRIVER_TRACE_CBID_cuD3D9ResourceGetMappedPointer_v2                              = 265,
    CUPTI_DRIVER_TRACE_CBID_cuD3D9ResourceGetMappedSize_v2                                 = 266,
    CUPTI_DRIVER_TRACE_CBID_cuD3D9ResourceGetMappedPitch_v2                                = 267,
    CUPTI_DRIVER_TRACE_CBID_cuD3D9MapVertexBuffer_v2                                       = 268,
    CUPTI_DRIVER_TRACE_CBID_cuGLMapBufferObject_v2                                         = 269,
    CUPTI_DRIVER_TRACE_CBID_cuGLMapBufferObjectAsync_v2                                    = 270,
    CUPTI_DRIVER_TRACE_CBID_cuMemHostAlloc_v2                                              = 271,
    CUPTI_DRIVER_TRACE_CBID_cuArrayCreate_v2                                               = 272,
    CUPTI_DRIVER_TRACE_CBID_cuArrayGetDescriptor_v2                                        = 273,
    CUPTI_DRIVER_TRACE_CBID_cuArray3DCreate_v2                                             = 274,
    CUPTI_DRIVER_TRACE_CBID_cuArray3DGetDescriptor_v2                                      = 275,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyHtoD_v2                                                = 276,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyHtoDAsync_v2                                           = 277,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyDtoH_v2                                                = 278,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyDtoHAsync_v2                                           = 279,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyDtoD_v2                                                = 280,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyDtoDAsync_v2                                           = 281,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyAtoH_v2                                                = 282,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyAtoHAsync_v2                                           = 283,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyAtoD_v2                                                = 284,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyDtoA_v2                                                = 285,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyAtoA_v2                                                = 286,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpy2D_v2                                                  = 287,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpy2DUnaligned_v2                                         = 288,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpy2DAsync_v2                                             = 289,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpy3D_v2                                                  = 290,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpy3DAsync_v2                                             = 291,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyHtoA_v2                                                = 292,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyHtoAAsync_v2                                           = 293,
    CUPTI_DRIVER_TRACE_CBID_cuMemAllocHost_v2                                              = 294,
    CUPTI_DRIVER_TRACE_CBID_cuStreamWaitEvent                                              = 295,
    CUPTI_DRIVER_TRACE_CBID_cuCtxGetApiVersion                                             = 296,
    CUPTI_DRIVER_TRACE_CBID_cuD3D10GetDirect3DDevice                                       = 297,
    CUPTI_DRIVER_TRACE_CBID_cuD3D11GetDirect3DDevice                                       = 298,
    CUPTI_DRIVER_TRACE_CBID_cuCtxGetCacheConfig                                            = 299,
    CUPTI_DRIVER_TRACE_CBID_cuCtxSetCacheConfig                                            = 300,
    CUPTI_DRIVER_TRACE_CBID_cuMemHostRegister                                              = 301,
    CUPTI_DRIVER_TRACE_CBID_cuMemHostUnregister                                            = 302,
    CUPTI_DRIVER_TRACE_CBID_cuCtxSetCurrent                                                = 303,
    CUPTI_DRIVER_TRACE_CBID_cuCtxGetCurrent                                                = 304,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpy                                                       = 305,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyAsync                                                  = 306,
    CUPTI_DRIVER_TRACE_CBID_cuLaunchKernel                                                 = 307,
    CUPTI_DRIVER_TRACE_CBID_cuProfilerStart                                                = 308,
    CUPTI_DRIVER_TRACE_CBID_cuProfilerStop                                                 = 309,
    CUPTI_DRIVER_TRACE_CBID_cuPointerGetAttribute                                          = 310,
    CUPTI_DRIVER_TRACE_CBID_cuProfilerInitialize                                           = 311,
    CUPTI_DRIVER_TRACE_CBID_cuDeviceCanAccessPeer                                          = 312,
    CUPTI_DRIVER_TRACE_CBID_cuCtxEnablePeerAccess                                          = 313,
    CUPTI_DRIVER_TRACE_CBID_cuCtxDisablePeerAccess                                         = 314,
    CUPTI_DRIVER_TRACE_CBID_cuMemPeerRegister                                              = 315,
    CUPTI_DRIVER_TRACE_CBID_cuMemPeerUnregister                                            = 316,
    CUPTI_DRIVER_TRACE_CBID_cuMemPeerGetDevicePointer                                      = 317,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyPeer                                                   = 318,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyPeerAsync                                              = 319,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpy3DPeer                                                 = 320,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpy3DPeerAsync                                            = 321,
    CUPTI_DRIVER_TRACE_CBID_cuCtxDestroy_v2                                                = 322,
    CUPTI_DRIVER_TRACE_CBID_cuCtxPushCurrent_v2                                            = 323,
    CUPTI_DRIVER_TRACE_CBID_cuCtxPopCurrent_v2                                             = 324,
    CUPTI_DRIVER_TRACE_CBID_cuEventDestroy_v2                                              = 325,
    CUPTI_DRIVER_TRACE_CBID_cuStreamDestroy_v2                                             = 326,
    CUPTI_DRIVER_TRACE_CBID_cuTexRefSetAddress2D_v3                                        = 327,
    CUPTI_DRIVER_TRACE_CBID_cuIpcGetMemHandle                                              = 328,
    CUPTI_DRIVER_TRACE_CBID_cuIpcOpenMemHandle                                             = 329,
    CUPTI_DRIVER_TRACE_CBID_cuIpcCloseMemHandle                                            = 330,
    CUPTI_DRIVER_TRACE_CBID_cuDeviceGetByPCIBusId                                          = 331,
    CUPTI_DRIVER_TRACE_CBID_cuDeviceGetPCIBusId                                            = 332,
    CUPTI_DRIVER_TRACE_CBID_cuGLGetDevices                                                 = 333,
    CUPTI_DRIVER_TRACE_CBID_cuIpcGetEventHandle                                            = 334,
    CUPTI_DRIVER_TRACE_CBID_cuIpcOpenEventHandle                                           = 335,
    CUPTI_DRIVER_TRACE_CBID_cuCtxSetSharedMemConfig                                        = 336,
    CUPTI_DRIVER_TRACE_CBID_cuCtxGetSharedMemConfig                                        = 337,
    CUPTI_DRIVER_TRACE_CBID_cuFuncSetSharedMemConfig                                       = 338,
    CUPTI_DRIVER_TRACE_CBID_cuTexObjectCreate                                              = 339,
    CUPTI_DRIVER_TRACE_CBID_cuTexObjectDestroy                                             = 340,
    CUPTI_DRIVER_TRACE_CBID_cuTexObjectGetResourceDesc                                     = 341,
    CUPTI_DRIVER_TRACE_CBID_cuTexObjectGetTextureDesc                                      = 342,
    CUPTI_DRIVER_TRACE_CBID_cuSurfObjectCreate                                             = 343,
    CUPTI_DRIVER_TRACE_CBID_cuSurfObjectDestroy                                            = 344,
    CUPTI_DRIVER_TRACE_CBID_cuSurfObjectGetResourceDesc                                    = 345,
    CUPTI_DRIVER_TRACE_CBID_cuStreamAddCallback                                            = 346,
    CUPTI_DRIVER_TRACE_CBID_cuMipmappedArrayCreate                                         = 347,
    CUPTI_DRIVER_TRACE_CBID_cuMipmappedArrayGetLevel                                       = 348,
    CUPTI_DRIVER_TRACE_CBID_cuMipmappedArrayDestroy                                        = 349,
    CUPTI_DRIVER_TRACE_CBID_cuTexRefSetMipmappedArray                                      = 350,
    CUPTI_DRIVER_TRACE_CBID_cuTexRefSetMipmapFilterMode                                    = 351,
    CUPTI_DRIVER_TRACE_CBID_cuTexRefSetMipmapLevelBias                                     = 352,
    CUPTI_DRIVER_TRACE_CBID_cuTexRefSetMipmapLevelClamp                                    = 353,
    CUPTI_DRIVER_TRACE_CBID_cuTexRefSetMaxAnisotropy                                       = 354,
    CUPTI_DRIVER_TRACE_CBID_cuTexRefGetMipmappedArray                                      = 355,
    CUPTI_DRIVER_TRACE_CBID_cuTexRefGetMipmapFilterMode                                    = 356,
    CUPTI_DRIVER_TRACE_CBID_cuTexRefGetMipmapLevelBias                                     = 357,
    CUPTI_DRIVER_TRACE_CBID_cuTexRefGetMipmapLevelClamp                                    = 358,
    CUPTI_DRIVER_TRACE_CBID_cuTexRefGetMaxAnisotropy                                       = 359,
    CUPTI_DRIVER_TRACE_CBID_cuGraphicsResourceGetMappedMipmappedArray                      = 360,
    CUPTI_DRIVER_TRACE_CBID_cuTexObjectGetResourceViewDesc                                 = 361,
    CUPTI_DRIVER_TRACE_CBID_cuLinkCreate                                                   = 362,
    CUPTI_DRIVER_TRACE_CBID_cuLinkAddData                                                  = 363,
    CUPTI_DRIVER_TRACE_CBID_cuLinkAddFile                                                  = 364,
    CUPTI_DRIVER_TRACE_CBID_cuLinkComplete                                                 = 365,
    CUPTI_DRIVER_TRACE_CBID_cuLinkDestroy                                                  = 366,
    CUPTI_DRIVER_TRACE_CBID_cuStreamCreateWithPriority                                     = 367,
    CUPTI_DRIVER_TRACE_CBID_cuStreamGetPriority                                            = 368,
    CUPTI_DRIVER_TRACE_CBID_cuStreamGetFlags                                               = 369,
    CUPTI_DRIVER_TRACE_CBID_cuCtxGetStreamPriorityRange                                    = 370,
    CUPTI_DRIVER_TRACE_CBID_cuMemAllocManaged                                              = 371,
    CUPTI_DRIVER_TRACE_CBID_cuGetErrorString                                               = 372,
    CUPTI_DRIVER_TRACE_CBID_cuGetErrorName                                                 = 373,
    CUPTI_DRIVER_TRACE_CBID_cuOccupancyMaxActiveBlocksPerMultiprocessor                    = 374,
    CUPTI_DRIVER_TRACE_CBID_cuCompilePtx                                                   = 375,
    CUPTI_DRIVER_TRACE_CBID_cuBinaryFree                                                   = 376,
    CUPTI_DRIVER_TRACE_CBID_cuStreamAttachMemAsync                                         = 377,
    CUPTI_DRIVER_TRACE_CBID_cuPointerSetAttribute                                          = 378,
    CUPTI_DRIVER_TRACE_CBID_cuMemHostRegister_v2                                           = 379,
    CUPTI_DRIVER_TRACE_CBID_cuGraphicsResourceSetMapFlags_v2                               = 380,
    CUPTI_DRIVER_TRACE_CBID_cuLinkCreate_v2                                                = 381,
    CUPTI_DRIVER_TRACE_CBID_cuLinkAddData_v2                                               = 382,
    CUPTI_DRIVER_TRACE_CBID_cuLinkAddFile_v2                                               = 383,
    CUPTI_DRIVER_TRACE_CBID_cuOccupancyMaxPotentialBlockSize                               = 384,
    CUPTI_DRIVER_TRACE_CBID_cuGLGetDevices_v2                                              = 385,
    CUPTI_DRIVER_TRACE_CBID_cuDevicePrimaryCtxRetain                                       = 386,
    CUPTI_DRIVER_TRACE_CBID_cuDevicePrimaryCtxRelease                                      = 387,
    CUPTI_DRIVER_TRACE_CBID_cuDevicePrimaryCtxSetFlags                                     = 388,
    CUPTI_DRIVER_TRACE_CBID_cuDevicePrimaryCtxReset                                        = 389,
    CUPTI_DRIVER_TRACE_CBID_cuGraphicsEGLRegisterImage                                     = 390,
    CUPTI_DRIVER_TRACE_CBID_cuCtxGetFlags                                                  = 391,
    CUPTI_DRIVER_TRACE_CBID_cuDevicePrimaryCtxGetState                                     = 392,
    CUPTI_DRIVER_TRACE_CBID_cuEGLStreamConsumerConnect                                     = 393,
    CUPTI_DRIVER_TRACE_CBID_cuEGLStreamConsumerDisconnect                                  = 394,
    CUPTI_DRIVER_TRACE_CBID_cuEGLStreamConsumerAcquireFrame                                = 395,
    CUPTI_DRIVER_TRACE_CBID_cuEGLStreamConsumerReleaseFrame                                = 396,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyHtoD_v2_ptds                                           = 397,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyDtoH_v2_ptds                                           = 398,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyDtoD_v2_ptds                                           = 399,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyDtoA_v2_ptds                                           = 400,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyAtoD_v2_ptds                                           = 401,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyHtoA_v2_ptds                                           = 402,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyAtoH_v2_ptds                                           = 403,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyAtoA_v2_ptds                                           = 404,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpy2D_v2_ptds                                             = 405,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpy2DUnaligned_v2_ptds                                    = 406,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpy3D_v2_ptds                                             = 407,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpy_ptds                                                  = 408,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyPeer_ptds                                              = 409,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpy3DPeer_ptds                                            = 410,
    CUPTI_DRIVER_TRACE_CBID_cuMemsetD8_v2_ptds                                             = 411,
    CUPTI_DRIVER_TRACE_CBID_cuMemsetD16_v2_ptds                                            = 412,
    CUPTI_DRIVER_TRACE_CBID_cuMemsetD32_v2_ptds                                            = 413,
    CUPTI_DRIVER_TRACE_CBID_cuMemsetD2D8_v2_ptds                                           = 414,
    CUPTI_DRIVER_TRACE_CBID_cuMemsetD2D16_v2_ptds                                          = 415,
    CUPTI_DRIVER_TRACE_CBID_cuMemsetD2D32_v2_ptds                                          = 416,
    CUPTI_DRIVER_TRACE_CBID_cuGLMapBufferObject_v2_ptds                                    = 417,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyAsync_ptsz                                             = 418,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyHtoAAsync_v2_ptsz                                      = 419,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyAtoHAsync_v2_ptsz                                      = 420,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyHtoDAsync_v2_ptsz                                      = 421,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyDtoHAsync_v2_ptsz                                      = 422,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyDtoDAsync_v2_ptsz                                      = 423,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpy2DAsync_v2_ptsz                                        = 424,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpy3DAsync_v2_ptsz                                        = 425,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpyPeerAsync_ptsz                                         = 426,
    CUPTI_DRIVER_TRACE_CBID_cuMemcpy3DPeerAsync_ptsz                                       = 427,
    CUPTI_DRIVER_TRACE_CBID_cuMemsetD8Async_ptsz                                           = 428,
    CUPTI_DRIVER_TRACE_CBID_cuMemsetD16Async_ptsz                                          = 429,
    CUPTI_DRIVER_TRACE_CBID_cuMemsetD32Async_ptsz                                          = 430,
    CUPTI_DRIVER_TRACE_CBID_cuMemsetD2D8Async_ptsz                                         = 431,
    CUPTI_DRIVER_TRACE_CBID_cuMemsetD2D16Async_ptsz                                        = 432,
    CUPTI_DRIVER_TRACE_CBID_cuMemsetD2D32Async_ptsz                                        = 433,
    CUPTI_DRIVER_TRACE_CBID_cuStreamGetPriority_ptsz                                       = 434,
    CUPTI_DRIVER_TRACE_CBID_cuStreamGetFlags_ptsz                                          = 435,
    CUPTI_DRIVER_TRACE_CBID_cuStreamWaitEvent_ptsz                                         = 436,
    CUPTI_DRIVER_TRACE_CBID_cuStreamAddCallback_ptsz                                       = 437,
    CUPTI_DRIVER_TRACE_CBID_cuStreamAttachMemAsync_ptsz                                    = 438,
    CUPTI_DRIVER_TRACE_CBID_cuStreamQuery_ptsz                                             = 439,
    CUPTI_DRIVER_TRACE_CBID_cuStreamSynchronize_ptsz                                       = 440,
    CUPTI_DRIVER_TRACE_CBID_cuEventRecord_ptsz                                             = 441,
    CUPTI_DRIVER_TRACE_CBID_cuLaunchKernel_ptsz                                            = 442,
    CUPTI_DRIVER_TRACE_CBID_cuGraphicsMapResources_ptsz                                    = 443,
    CUPTI_DRIVER_TRACE_CBID_cuGraphicsUnmapResources_ptsz                                  = 444,
    CUPTI_DRIVER_TRACE_CBID_cuGLMapBufferObjectAsync_v2_ptsz                               = 445,
    CUPTI_DRIVER_TRACE_CBID_cuEGLStreamProducerConnect                                     = 446,
    CUPTI_DRIVER_TRACE_CBID_cuEGLStreamProducerDisconnect                                  = 447,
    CUPTI_DRIVER_TRACE_CBID_cuEGLStreamProducerPresentFrame                                = 448,
    CUPTI_DRIVER_TRACE_CBID_cuGraphicsResourceGetMappedEglFrame                            = 449,
    CUPTI_DRIVER_TRACE_CBID_cuPointerGetAttributes                                         = 450,
    CUPTI_DRIVER_TRACE_CBID_cuOccupancyMaxActiveBlocksPerMultiprocessorWithFlags           = 451,
    CUPTI_DRIVER_TRACE_CBID_cuOccupancyMaxPotentialBlockSizeWithFlags                      = 452,
    CUPTI_DRIVER_TRACE_CBID_cuEGLStreamProducerReturnFrame                                 = 453,
    CUPTI_DRIVER_TRACE_CBID_cuDeviceGetP2PAttribute                                        = 454,
    CUPTI_DRIVER_TRACE_CBID_cuTexRefSetBorderColor                                         = 455,
    CUPTI_DRIVER_TRACE_CBID_cuTexRefGetBorderColor                                         = 456,
    CUPTI_DRIVER_TRACE_CBID_cuMemAdvise                                                    = 457,
    CUPTI_DRIVER_TRACE_CBID_cuStreamWaitValue32                                            = 458,
    CUPTI_DRIVER_TRACE_CBID_cuStreamWaitValue32_ptsz                                       = 459,
    CUPTI_DRIVER_TRACE_CBID_cuStreamWriteValue32                                           = 460,
    CUPTI_DRIVER_TRACE_CBID_cuStreamWriteValue32_ptsz                                      = 461,
    CUPTI_DRIVER_TRACE_CBID_cuStreamBatchMemOp                                             = 462,
    CUPTI_DRIVER_TRACE_CBID_cuStreamBatchMemOp_ptsz                                        = 463,
    CUPTI_DRIVER_TRACE_CBID_cuNVNbufferGetPointer                                          = 464,
    CUPTI_DRIVER_TRACE_CBID_cuNVNtextureGetArray                                           = 465,
    CUPTI_DRIVER_TRACE_CBID_cuNNSetAllocator                                               = 466,
    CUPTI_DRIVER_TRACE_CBID_cuMemPrefetchAsync                                             = 467,
    CUPTI_DRIVER_TRACE_CBID_cuMemPrefetchAsync_ptsz                                        = 468,
    CUPTI_DRIVER_TRACE_CBID_cuEventCreateFromNVNSync                                       = 469,
    CUPTI_DRIVER_TRACE_CBID_cuEGLStreamConsumerConnectWithFlags                            = 470,
    CUPTI_DRIVER_TRACE_CBID_cuMemRangeGetAttribute                                         = 471,
    CUPTI_DRIVER_TRACE_CBID_cuMemRangeGetAttributes                                        = 472,
    CUPTI_DRIVER_TRACE_CBID_cuStreamWaitValue64                                            = 473,
    CUPTI_DRIVER_TRACE_CBID_cuStreamWaitValue64_ptsz                                       = 474,
    CUPTI_DRIVER_TRACE_CBID_cuStreamWriteValue64                                           = 475,
    CUPTI_DRIVER_TRACE_CBID_cuStreamWriteValue64_ptsz                                      = 476,
    CUPTI_DRIVER_TRACE_CBID_cuLaunchCooperativeKernel                                      = 477,
    CUPTI_DRIVER_TRACE_CBID_cuLaunchCooperativeKernel_ptsz                                 = 478,
    CUPTI_DRIVER_TRACE_CBID_cuEventCreateFromEGLSync                                       = 479,
    CUPTI_DRIVER_TRACE_CBID_cuLaunchCooperativeKernelMultiDevice                           = 480,
    CUPTI_DRIVER_TRACE_CBID_cuFuncSetAttribute                                             = 481,
    CUPTI_DRIVER_TRACE_CBID_cuDeviceGetUuid                                                = 482,
    CUPTI_DRIVER_TRACE_CBID_cuStreamGetCtx                                                 = 483,
    CUPTI_DRIVER_TRACE_CBID_cuStreamGetCtx_ptsz                                            = 484,
    CUPTI_DRIVER_TRACE_CBID_cuImportExternalMemory                                         = 485,
    CUPTI_DRIVER_TRACE_CBID_cuExternalMemoryGetMappedBuffer                                = 486,
    CUPTI_DRIVER_TRACE_CBID_cuExternalMemoryGetMappedMipmappedArray                        = 487,
    CUPTI_DRIVER_TRACE_CBID_cuDestroyExternalMemory                                        = 488,
    CUPTI_DRIVER_TRACE_CBID_cuImportExternalSemaphore                                      = 489,
    CUPTI_DRIVER_TRACE_CBID_cuSignalExternalSemaphoresAsync                                = 490,
    CUPTI_DRIVER_TRACE_CBID_cuSignalExternalSemaphoresAsync_ptsz                           = 491,
    CUPTI_DRIVER_TRACE_CBID_cuWaitExternalSemaphoresAsync                                  = 492,
    CUPTI_DRIVER_TRACE_CBID_cuWaitExternalSemaphoresAsync_ptsz                             = 493,
    CUPTI_DRIVER_TRACE_CBID_cuDestroyExternalSemaphore                                     = 494,
    CUPTI_DRIVER_TRACE_CBID_cuStreamBeginCapture                                           = 495,
    CUPTI_DRIVER_TRACE_CBID_cuStreamBeginCapture_ptsz                                      = 496,
    CUPTI_DRIVER_TRACE_CBID_cuStreamEndCapture                                             = 497,
    CUPTI_DRIVER_TRACE_CBID_cuStreamEndCapture_ptsz                                        = 498,
    CUPTI_DRIVER_TRACE_CBID_cuStreamIsCapturing                                            = 499,
    CUPTI_DRIVER_TRACE_CBID_cuStreamIsCapturing_ptsz                                       = 500,
    CUPTI_DRIVER_TRACE_CBID_cuGraphCreate                                                  = 501,
    CUPTI_DRIVER_TRACE_CBID_cuGraphAddKernelNode                                           = 502,
    CUPTI_DRIVER_TRACE_CBID_cuGraphKernelNodeGetParams                                     = 503,
    CUPTI_DRIVER_TRACE_CBID_cuGraphAddMemcpyNode                                           = 504,
    CUPTI_DRIVER_TRACE_CBID_cuGraphMemcpyNodeGetParams                                     = 505,
    CUPTI_DRIVER_TRACE_CBID_cuGraphAddMemsetNode                                           = 506,
    CUPTI_DRIVER_TRACE_CBID_cuGraphMemsetNodeGetParams                                     = 507,
    CUPTI_DRIVER_TRACE_CBID_cuGraphMemsetNodeSetParams                                     = 508,
    CUPTI_DRIVER_TRACE_CBID_cuGraphNodeGetType                                             = 509,
    CUPTI_DRIVER_TRACE_CBID_cuGraphGetRootNodes                                            = 510,
    CUPTI_DRIVER_TRACE_CBID_cuGraphNodeGetDependencies                                     = 511,
    CUPTI_DRIVER_TRACE_CBID_cuGraphNodeGetDependentNodes                                   = 512,
    CUPTI_DRIVER_TRACE_CBID_cuGraphInstantiate                                             = 513,
    CUPTI_DRIVER_TRACE_CBID_cuGraphLaunch                                                  = 514,
    CUPTI_DRIVER_TRACE_CBID_cuGraphLaunch_ptsz                                             = 515,
    CUPTI_DRIVER_TRACE_CBID_cuGraphExecDestroy                                             = 516,
    CUPTI_DRIVER_TRACE_CBID_cuGraphDestroy                                                 = 517,
    CUPTI_DRIVER_TRACE_CBID_cuGraphAddDependencies                                         = 518,
    CUPTI_DRIVER_TRACE_CBID_cuGraphRemoveDependencies                                      = 519,
    CUPTI_DRIVER_TRACE_CBID_cuGraphMemcpyNodeSetParams                                     = 520,
    CUPTI_DRIVER_TRACE_CBID_cuGraphKernelNodeSetParams                                     = 521,
    CUPTI_DRIVER_TRACE_CBID_cuGraphDestroyNode                                             = 522,
    CUPTI_DRIVER_TRACE_CBID_cuGraphClone                                                   = 523,
    CUPTI_DRIVER_TRACE_CBID_cuGraphNodeFindInClone                                         = 524,
    CUPTI_DRIVER_TRACE_CBID_cuGraphAddChildGraphNode                                       = 525,
    CUPTI_DRIVER_TRACE_CBID_cuGraphAddEmptyNode                                            = 526,
    CUPTI_DRIVER_TRACE_CBID_cuLaunchHostFunc                                               = 527,
    CUPTI_DRIVER_TRACE_CBID_cuLaunchHostFunc_ptsz                                          = 528,
    CUPTI_DRIVER_TRACE_CBID_cuGraphChildGraphNodeGetGraph                                  = 529,
    CUPTI_DRIVER_TRACE_CBID_cuGraphAddHostNode                                             = 530,
    CUPTI_DRIVER_TRACE_CBID_cuGraphHostNodeGetParams                                       = 531,
    CUPTI_DRIVER_TRACE_CBID_cuDeviceGetLuid                                                = 532,
    CUPTI_DRIVER_TRACE_CBID_cuGraphHostNodeSetParams                                       = 533,
    CUPTI_DRIVER_TRACE_CBID_cuGraphGetNodes                                                = 534,
    CUPTI_DRIVER_TRACE_CBID_cuGraphGetEdges                                                = 535,
    CUPTI_DRIVER_TRACE_CBID_cuStreamGetCaptureInfo                                         = 536,
    CUPTI_DRIVER_TRACE_CBID_cuStreamGetCaptureInfo_ptsz                                    = 537,
    CUPTI_DRIVER_TRACE_CBID_cuGraphExecKernelNodeSetParams                                 = 538,
    CUPTI_DRIVER_TRACE_CBID_cuStreamBeginCapture_v2                                        = 539,
    CUPTI_DRIVER_TRACE_CBID_cuStreamBeginCapture_v2_ptsz                                   = 540,
    CUPTI_DRIVER_TRACE_CBID_cuThreadExchangeStreamCaptureMode                              = 541,
    CUPTI_DRIVER_TRACE_CBID_cuDeviceGetNvSciSyncAttributes                                 = 542,
    CUPTI_DRIVER_TRACE_CBID_cuOccupancyAvailableDynamicSMemPerBlock                        = 543,
    CUPTI_DRIVER_TRACE_CBID_cuDevicePrimaryCtxRelease_v2                                   = 544,
    CUPTI_DRIVER_TRACE_CBID_cuDevicePrimaryCtxReset_v2                                     = 545,
    CUPTI_DRIVER_TRACE_CBID_cuDevicePrimaryCtxSetFlags_v2                                  = 546,
    CUPTI_DRIVER_TRACE_CBID_cuMemAddressReserve                                            = 547,
    CUPTI_DRIVER_TRACE_CBID_cuMemAddressFree                                               = 548,
    CUPTI_DRIVER_TRACE_CBID_cuMemCreate                                                    = 549,
    CUPTI_DRIVER_TRACE_CBID_cuMemRelease                                                   = 550,
    CUPTI_DRIVER_TRACE_CBID_cuMemMap                                                       = 551,
    CUPTI_DRIVER_TRACE_CBID_cuMemUnmap                                                     = 552,
    CUPTI_DRIVER_TRACE_CBID_cuMemSetAccess                                                 = 553,
    CUPTI_DRIVER_TRACE_CBID_cuMemExportToShareableHandle                                   = 554,
    CUPTI_DRIVER_TRACE_CBID_cuMemImportFromShareableHandle                                 = 555,
    CUPTI_DRIVER_TRACE_CBID_cuMemGetAllocationGranularity                                  = 556,
    CUPTI_DRIVER_TRACE_CBID_cuMemGetAllocationPropertiesFromHandle                         = 557,
    CUPTI_DRIVER_TRACE_CBID_cuMemGetAccess                                                 = 558,
    CUPTI_DRIVER_TRACE_CBID_cuStreamSetFlags                                               = 559,
    CUPTI_DRIVER_TRACE_CBID_cuStreamSetFlags_ptsz                                          = 560,
    CUPTI_DRIVER_TRACE_CBID_cuGraphExecUpdate                                              = 561,
    CUPTI_DRIVER_TRACE_CBID_cuGraphExecMemcpyNodeSetParams                                 = 562,
    CUPTI_DRIVER_TRACE_CBID_cuGraphExecMemsetNodeSetParams                                 = 563,
    CUPTI_DRIVER_TRACE_CBID_cuGraphExecHostNodeSetParams                                   = 564,
    CUPTI_DRIVER_TRACE_CBID_cuMemRetainAllocationHandle                                    = 565,
    CUPTI_DRIVER_TRACE_CBID_cuFuncGetModule                                                = 566,
    CUPTI_DRIVER_TRACE_CBID_cuIpcOpenMemHandle_v2                                          = 567,
    CUPTI_DRIVER_TRACE_CBID_cuCtxResetPersistingL2Cache                                    = 568,
    CUPTI_DRIVER_TRACE_CBID_cuGraphKernelNodeCopyAttributes                                = 569,
    CUPTI_DRIVER_TRACE_CBID_cuGraphKernelNodeGetAttribute                                  = 570,
    CUPTI_DRIVER_TRACE_CBID_cuGraphKernelNodeSetAttribute                                  = 571,
    CUPTI_DRIVER_TRACE_CBID_cuStreamCopyAttributes                                         = 572,
    CUPTI_DRIVER_TRACE_CBID_cuStreamCopyAttributes_ptsz                                    = 573,
    CUPTI_DRIVER_TRACE_CBID_cuStreamGetAttribute                                           = 574,
    CUPTI_DRIVER_TRACE_CBID_cuStreamGetAttribute_ptsz                                      = 575,
    CUPTI_DRIVER_TRACE_CBID_cuStreamSetAttribute                                           = 576,
    CUPTI_DRIVER_TRACE_CBID_cuStreamSetAttribute_ptsz                                      = 577,
    CUPTI_DRIVER_TRACE_CBID_cuGraphInstantiate_v2                                          = 578,
    CUPTI_DRIVER_TRACE_CBID_cuDeviceGetTexture1DLinearMaxWidth                             = 579,
    CUPTI_DRIVER_TRACE_CBID_cuGraphUpload                                                  = 580,
    CUPTI_DRIVER_TRACE_CBID_cuGraphUpload_ptsz                                             = 581,
    CUPTI_DRIVER_TRACE_CBID_cuArrayGetSparseProperties                                     = 582,
    CUPTI_DRIVER_TRACE_CBID_cuMipmappedArrayGetSparseProperties                            = 583,
    CUPTI_DRIVER_TRACE_CBID_cuMemMapArrayAsync                                             = 584,
    CUPTI_DRIVER_TRACE_CBID_cuMemMapArrayAsync_ptsz                                        = 585,
    CUPTI_DRIVER_TRACE_CBID_cuGraphExecChildGraphNodeSetParams                             = 586,
    CUPTI_DRIVER_TRACE_CBID_cuEventRecordWithFlags                                         = 587,
    CUPTI_DRIVER_TRACE_CBID_cuEventRecordWithFlags_ptsz                                    = 588,
    CUPTI_DRIVER_TRACE_CBID_cuGraphAddEventRecordNode                                      = 589,
    CUPTI_DRIVER_TRACE_CBID_cuGraphAddEventWaitNode                                        = 590,
    CUPTI_DRIVER_TRACE_CBID_cuGraphEventRecordNodeGetEvent                                 = 591,
    CUPTI_DRIVER_TRACE_CBID_cuGraphEventWaitNodeGetEvent                                   = 592,
    CUPTI_DRIVER_TRACE_CBID_cuGraphEventRecordNodeSetEvent                                 = 593,
    CUPTI_DRIVER_TRACE_CBID_cuGraphEventWaitNodeSetEvent                                   = 594,
    CUPTI_DRIVER_TRACE_CBID_cuGraphExecEventRecordNodeSetEvent                             = 595,
    CUPTI_DRIVER_TRACE_CBID_cuGraphExecEventWaitNodeSetEvent                               = 596,
    CUPTI_DRIVER_TRACE_CBID_cuArrayGetPlane                                                = 597,
    CUPTI_DRIVER_TRACE_CBID_cuMemAllocAsync                                                = 598,
    CUPTI_DRIVER_TRACE_CBID_cuMemAllocAsync_ptsz                                           = 599,
    CUPTI_DRIVER_TRACE_CBID_cuMemFreeAsync                                                 = 600,
    CUPTI_DRIVER_TRACE_CBID_cuMemFreeAsync_ptsz                                            = 601,
    CUPTI_DRIVER_TRACE_CBID_cuMemPoolTrimTo                                                = 602,
    CUPTI_DRIVER_TRACE_CBID_cuMemPoolSetAttribute                                          = 603,
    CUPTI_DRIVER_TRACE_CBID_cuMemPoolGetAttribute                                          = 604,
    CUPTI_DRIVER_TRACE_CBID_cuMemPoolSetAccess                                             = 605,
    CUPTI_DRIVER_TRACE_CBID_cuDeviceGetDefaultMemPool                                      = 606,
    CUPTI_DRIVER_TRACE_CBID_cuMemPoolCreate                                                = 607,
    CUPTI_DRIVER_TRACE_CBID_cuMemPoolDestroy                                               = 608,
    CUPTI_DRIVER_TRACE_CBID_cuDeviceSetMemPool                                             = 609,
    CUPTI_DRIVER_TRACE_CBID_cuDeviceGetMemPool                                             = 610,
    CUPTI_DRIVER_TRACE_CBID_cuMemAllocFromPoolAsync                                        = 611,
    CUPTI_DRIVER_TRACE_CBID_cuMemAllocFromPoolAsync_ptsz                                   = 612,
    CUPTI_DRIVER_TRACE_CBID_cuMemPoolExportToShareableHandle                               = 613,
    CUPTI_DRIVER_TRACE_CBID_cuMemPoolImportFromShareableHandle                             = 614,
    CUPTI_DRIVER_TRACE_CBID_cuMemPoolExportPointer                                         = 615,
    CUPTI_DRIVER_TRACE_CBID_cuMemPoolImportPointer                                         = 616,
    CUPTI_DRIVER_TRACE_CBID_cuMemPoolGetAccess                                             = 617,
    CUPTI_DRIVER_TRACE_CBID_cuGraphAddExternalSemaphoresSignalNode                         = 618,
    CUPTI_DRIVER_TRACE_CBID_cuGraphExternalSemaphoresSignalNodeGetParams                   = 619,
    CUPTI_DRIVER_TRACE_CBID_cuGraphExternalSemaphoresSignalNodeSetParams                   = 620,
    CUPTI_DRIVER_TRACE_CBID_cuGraphAddExternalSemaphoresWaitNode                           = 621,
    CUPTI_DRIVER_TRACE_CBID_cuGraphExternalSemaphoresWaitNodeGetParams                     = 622,
    CUPTI_DRIVER_TRACE_CBID_cuGraphExternalSemaphoresWaitNodeSetParams                     = 623,
    CUPTI_DRIVER_TRACE_CBID_cuGraphExecExternalSemaphoresSignalNodeSetParams               = 624,
    CUPTI_DRIVER_TRACE_CBID_cuGraphExecExternalSemaphoresWaitNodeSetParams                 = 625,
    CUPTI_DRIVER_TRACE_CBID_cuGetProcAddress                                               = 626,
    CUPTI_DRIVER_TRACE_CBID_cuFlushGPUDirectRDMAWrites                                     = 627,
    CUPTI_DRIVER_TRACE_CBID_cuGraphDebugDotPrint                                           = 628,
    CUPTI_DRIVER_TRACE_CBID_cuStreamGetCaptureInfo_v2                                      = 629,
    CUPTI_DRIVER_TRACE_CBID_cuStreamGetCaptureInfo_v2_ptsz                                 = 630,
    CUPTI_DRIVER_TRACE_CBID_cuStreamUpdateCaptureDependencies                              = 631,
    CUPTI_DRIVER_TRACE_CBID_cuStreamUpdateCaptureDependencies_ptsz                         = 632,
    CUPTI_DRIVER_TRACE_CBID_cuUserObjectCreate                                             = 633,
    CUPTI_DRIVER_TRACE_CBID_cuUserObjectRetain                                             = 634,
    CUPTI_DRIVER_TRACE_CBID_cuUserObjectRelease                                            = 635,
    CUPTI_DRIVER_TRACE_CBID_cuGraphRetainUserObject                                        = 636,
    CUPTI_DRIVER_TRACE_CBID_cuGraphReleaseUserObject                                       = 637,
    CUPTI_DRIVER_TRACE_CBID_cuGraphAddMemAllocNode                                         = 638,
    CUPTI_DRIVER_TRACE_CBID_cuGraphAddMemFreeNode                                          = 639,
    CUPTI_DRIVER_TRACE_CBID_cuDeviceGraphMemTrim                                           = 640,
    CUPTI_DRIVER_TRACE_CBID_cuDeviceGetGraphMemAttribute                                   = 641,
    CUPTI_DRIVER_TRACE_CBID_cuDeviceSetGraphMemAttribute                                   = 642,
    CUPTI_DRIVER_TRACE_CBID_cuGraphInstantiateWithFlags                                    = 643,
    CUPTI_DRIVER_TRACE_CBID_cuDeviceGetExecAffinitySupport                                 = 644,
    CUPTI_DRIVER_TRACE_CBID_cuCtxCreate_v3                                                 = 645,
    CUPTI_DRIVER_TRACE_CBID_cuCtxGetExecAffinity                                           = 646,
    CUPTI_DRIVER_TRACE_CBID_cuDeviceGetUuid_v2                                             = 647,
    CUPTI_DRIVER_TRACE_CBID_cuGraphMemAllocNodeGetParams                                   = 648,
    CUPTI_DRIVER_TRACE_CBID_cuGraphMemFreeNodeGetParams                                    = 649,
    CUPTI_DRIVER_TRACE_CBID_cuGraphNodeSetEnabled                                          = 650,
    CUPTI_DRIVER_TRACE_CBID_cuGraphNodeGetEnabled                                          = 651,
    CUPTI_DRIVER_TRACE_CBID_cuLaunchKernelEx                                               = 652,
    CUPTI_DRIVER_TRACE_CBID_cuLaunchKernelEx_ptsz                                          = 653,
    CUPTI_DRIVER_TRACE_CBID_cuArrayGetMemoryRequirements                                   = 654,
    CUPTI_DRIVER_TRACE_CBID_cuMipmappedArrayGetMemoryRequirements                          = 655,
    CUPTI_DRIVER_TRACE_CBID_cuGraphInstantiateWithParams                                   = 656,
    CUPTI_DRIVER_TRACE_CBID_cuGraphInstantiateWithParams_ptsz                              = 657,
    CUPTI_DRIVER_TRACE_CBID_cuGraphExecGetFlags                                            = 658,
    CUPTI_DRIVER_TRACE_CBID_cuStreamWaitValue32_v2                                         = 659,
    CUPTI_DRIVER_TRACE_CBID_cuStreamWaitValue32_v2_ptsz                                    = 660,
    CUPTI_DRIVER_TRACE_CBID_cuStreamWaitValue64_v2                                         = 661,
    CUPTI_DRIVER_TRACE_CBID_cuStreamWaitValue64_v2_ptsz                                    = 662,
    CUPTI_DRIVER_TRACE_CBID_cuStreamWriteValue32_v2                                        = 663,
    CUPTI_DRIVER_TRACE_CBID_cuStreamWriteValue32_v2_ptsz                                   = 664,
    CUPTI_DRIVER_TRACE_CBID_cuStreamWriteValue64_v2                                        = 665,
    CUPTI_DRIVER_TRACE_CBID_cuStreamWriteValue64_v2_ptsz                                   = 666,
    CUPTI_DRIVER_TRACE_CBID_cuStreamBatchMemOp_v2                                          = 667,
    CUPTI_DRIVER_TRACE_CBID_cuStreamBatchMemOp_v2_ptsz                                     = 668,
    CUPTI_DRIVER_TRACE_CBID_cuGraphAddBatchMemOpNode                                       = 669,
    CUPTI_DRIVER_TRACE_CBID_cuGraphBatchMemOpNodeGetParams                                 = 670,
    CUPTI_DRIVER_TRACE_CBID_cuGraphBatchMemOpNodeSetParams                                 = 671,
    CUPTI_DRIVER_TRACE_CBID_cuGraphExecBatchMemOpNodeSetParams                             = 672,
    CUPTI_DRIVER_TRACE_CBID_cuModuleGetLoadingMode                                         = 673,
    CUPTI_DRIVER_TRACE_CBID_cuMemGetHandleForAddressRange                                  = 674,
    CUPTI_DRIVER_TRACE_CBID_cuOccupancyMaxPotentialClusterSize                             = 675,
    CUPTI_DRIVER_TRACE_CBID_cuOccupancyMaxActiveClusters                                   = 676,
    CUPTI_DRIVER_TRACE_CBID_cuGetProcAddress_v2                                            = 677,
    CUPTI_DRIVER_TRACE_CBID_cuLibraryLoadData                                              = 678,
    CUPTI_DRIVER_TRACE_CBID_cuLibraryLoadFromFile                                          = 679,
    CUPTI_DRIVER_TRACE_CBID_cuLibraryUnload                                                = 680,
    CUPTI_DRIVER_TRACE_CBID_cuLibraryGetKernel                                             = 681,
    CUPTI_DRIVER_TRACE_CBID_cuLibraryGetModule                                             = 682,
    CUPTI_DRIVER_TRACE_CBID_cuKernelGetFunction                                            = 683,
    CUPTI_DRIVER_TRACE_CBID_cuLibraryGetGlobal                                             = 684,
    CUPTI_DRIVER_TRACE_CBID_cuLibraryGetManaged                                            = 685,
    CUPTI_DRIVER_TRACE_CBID_cuKernelGetAttribute                                           = 686,
    CUPTI_DRIVER_TRACE_CBID_cuKernelSetAttribute                                           = 687,
    CUPTI_DRIVER_TRACE_CBID_cuKernelSetCacheConfig                                         = 688,
    CUPTI_DRIVER_TRACE_CBID_cuGraphAddKernelNode_v2                                        = 689,
    CUPTI_DRIVER_TRACE_CBID_cuGraphKernelNodeGetParams_v2                                  = 690,
    CUPTI_DRIVER_TRACE_CBID_cuGraphKernelNodeSetParams_v2                                  = 691,
    CUPTI_DRIVER_TRACE_CBID_cuGraphExecKernelNodeSetParams_v2                              = 692,
    CUPTI_DRIVER_TRACE_CBID_cuStreamGetId                                                  = 693,
    CUPTI_DRIVER_TRACE_CBID_cuStreamGetId_ptsz                                             = 694,
    CUPTI_DRIVER_TRACE_CBID_cuCtxGetId                                                     = 695,
    CUPTI_DRIVER_TRACE_CBID_cuGraphExecUpdate_v2                                           = 696,
    CUPTI_DRIVER_TRACE_CBID_cuTensorMapEncodeTiled                                         = 697,
    CUPTI_DRIVER_TRACE_CBID_cuTensorMapEncodeIm2col                                        = 698,
    CUPTI_DRIVER_TRACE_CBID_cuTensorMapReplaceAddress                                      = 699,
    CUPTI_DRIVER_TRACE_CBID_cuLibraryGetUnifiedFunction                                    = 700,
    CUPTI_DRIVER_TRACE_CBID_cuCoredumpGetAttribute                                         = 701,
    CUPTI_DRIVER_TRACE_CBID_cuCoredumpGetAttributeGlobal                                   = 702,
    CUPTI_DRIVER_TRACE_CBID_cuCoredumpSetAttribute                                         = 703,
    CUPTI_DRIVER_TRACE_CBID_cuCoredumpSetAttributeGlobal                                   = 704,
    CUPTI_DRIVER_TRACE_CBID_cuCtxSetFlags                                                  = 705,
    CUPTI_DRIVER_TRACE_CBID_cuMulticastCreate                                              = 706,
    CUPTI_DRIVER_TRACE_CBID_cuMulticastAddDevice                                           = 707,
    CUPTI_DRIVER_TRACE_CBID_cuMulticastBindMem                                             = 708,
    CUPTI_DRIVER_TRACE_CBID_cuMulticastBindAddr                                            = 709,
    CUPTI_DRIVER_TRACE_CBID_cuMulticastUnbind                                              = 710,
    CUPTI_DRIVER_TRACE_CBID_cuMulticastGetGranularity                                      = 711,
    CUPTI_DRIVER_TRACE_CBID_cuGraphAddNode                                                 = 712,
    CUPTI_DRIVER_TRACE_CBID_cuGraphNodeSetParams                                           = 713,
    CUPTI_DRIVER_TRACE_CBID_cuGraphExecNodeSetParams                                       = 714,
    CUPTI_DRIVER_TRACE_CBID_cuMemAdvise_v2                                                 = 715,
    CUPTI_DRIVER_TRACE_CBID_cuMemPrefetchAsync_v2                                          = 716,
    CUPTI_DRIVER_TRACE_CBID_cuMemPrefetchAsync_v2_ptsz                                     = 717,
    CUPTI_DRIVER_TRACE_CBID_cuFuncGetName                                                  = 718,
    CUPTI_DRIVER_TRACE_CBID_cuKernelGetName                                                = 719,
    CUPTI_DRIVER_TRACE_CBID_cuStreamBeginCaptureToGraph                                    = 720,
    CUPTI_DRIVER_TRACE_CBID_cuStreamBeginCaptureToGraph_ptsz                               = 721,
    CUPTI_DRIVER_TRACE_CBID_cuGraphConditionalHandleCreate                                 = 722,
    CUPTI_DRIVER_TRACE_CBID_cuGraphAddNode_v2                                              = 723,
    CUPTI_DRIVER_TRACE_CBID_cuGraphGetEdges_v2                                             = 724,
    CUPTI_DRIVER_TRACE_CBID_cuGraphNodeGetDependencies_v2                                  = 725,
    CUPTI_DRIVER_TRACE_CBID_cuGraphNodeGetDependentNodes_v2                                = 726,
    CUPTI_DRIVER_TRACE_CBID_cuGraphAddDependencies_v2                                      = 727,
    CUPTI_DRIVER_TRACE_CBID_cuGraphRemoveDependencies_v2                                   = 728,
    CUPTI_DRIVER_TRACE_CBID_cuStreamGetCaptureInfo_v3                                      = 729,
    CUPTI_DRIVER_TRACE_CBID_cuStreamGetCaptureInfo_v3_ptsz                                 = 730,
    CUPTI_DRIVER_TRACE_CBID_cuStreamUpdateCaptureDependencies_v2                           = 731,
    CUPTI_DRIVER_TRACE_CBID_cuStreamUpdateCaptureDependencies_v2_ptsz                      = 732,
    CUPTI_DRIVER_TRACE_CBID_cuFuncGetParamInfo                                             = 733,
    CUPTI_DRIVER_TRACE_CBID_cuKernelGetParamInfo                                           = 734,
    CUPTI_DRIVER_TRACE_CBID_cuDeviceRegisterAsyncNotification                              = 735,
    CUPTI_DRIVER_TRACE_CBID_cuDeviceUnregisterAsyncNotification                            = 736,
    CUPTI_DRIVER_TRACE_CBID_cuModuleGetFunctionCount                                       = 737,
    CUPTI_DRIVER_TRACE_CBID_cuModuleEnumerateFunctions                                     = 738,
    CUPTI_DRIVER_TRACE_CBID_cuLibraryGetKernelCount                                        = 739,
    CUPTI_DRIVER_TRACE_CBID_cuLibraryEnumerateKernels                                      = 740,
    CUPTI_DRIVER_TRACE_CBID_cuFuncIsLoaded                                                 = 741,
    CUPTI_DRIVER_TRACE_CBID_cuFuncLoad                                                     = 742,
    CUPTI_DRIVER_TRACE_CBID_cuGreenCtxCreate                                               = 743,
    CUPTI_DRIVER_TRACE_CBID_cuGreenCtxDestroy                                              = 744,
    CUPTI_DRIVER_TRACE_CBID_cuDeviceGetDevResource                                         = 745,
    CUPTI_DRIVER_TRACE_CBID_cuCtxGetDevResource                                            = 746,
    CUPTI_DRIVER_TRACE_CBID_cuGreenCtxGetDevResource                                       = 747,
    CUPTI_DRIVER_TRACE_CBID_cuDevResourceGenerateDesc                                      = 748,
    CUPTI_DRIVER_TRACE_CBID_cuGreenCtxRecordEvent                                          = 749,
    CUPTI_DRIVER_TRACE_CBID_cuGreenCtxWaitEvent                                            = 750,
    CUPTI_DRIVER_TRACE_CBID_cuDevSmResourceSplitByCount                                    = 751,
    CUPTI_DRIVER_TRACE_CBID_cuStreamGetGreenCtx                                            = 752,
    CUPTI_DRIVER_TRACE_CBID_cuCtxFromGreenCtx                                              = 753,
    CUPTI_DRIVER_TRACE_CBID_SIZE                                                           = 754,
    CUPTI_DRIVER_TRACE_CBID_FORCE_INT                                                      = 0x7fffffff
} CUpti_driver_api_trace_cbid;

